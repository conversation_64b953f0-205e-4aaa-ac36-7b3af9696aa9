import React, {
  lazy,
  Suspense,
  useEffect,
  useCallback,
  useMemo,
  Fragment,
} from 'react';
import {
  Backdrop,
  Box,
  Button,
  CircularProgress,
  Collapse,
  Divider,
  IconButton,
  Paper,
  Tooltip,
  Typography,
} from '@mui/material';
import { useLocation, useNavigate } from 'react-router';
import find from 'lodash/find';
import forEach from 'lodash/forEach';
import spacetime from 'spacetime';
import { useSnackbar } from 'notistack';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import { ASSET_MGMT_SITE_LIST } from '../../../constants/routes';
import { useMergeState } from '../../../hooks/useMergeStates';
import {
  getAllCustomAttributeDefinition,
  getSiteData,
} from '../../../services/entityServices';
import {
  ICustomAttributeDefinitionWithStaged,
  IUpdateCustomAttributeDeploymentType,
} from '../../../constants/types';
import { generateUUIDV4, hasUserRole } from '../../../utils/helpers';
import BulkEditorFormRenderer from '../../SiteAttributes/BulkEditorFormRenderer';
import UserRoles from '../../../constants/userRoles';
import { Entities } from '../../../constants/entities';
import sleep from '../../../utils/sleep';
import { useCustomAttributesbySiteId } from '../../../services/use-query';
import { theme } from '../../../constants/theme';
import { CustomAttributeConfirmDialogType } from '../../../components/modals/CustomAttributeConfirmDialog/types';
import {
  CustomAttributeFormValue,
  SingleSiteEditorFormState,
  SingleSiteEditorFormProps,
} from './types';

const CustomAttributeConfirmDialog = lazy(
  () => import('../../../components/modals/CustomAttributeConfirmDialog')
);

const SingleSiteEditorForm = ({
  groupedSiteAttributes,
  siteId,
  updatedAt,
  isAttributesLoading,
  refetchSiteAttributes,
  renderKey,
  children,
}: SingleSiteEditorFormProps) => {
  const navigate = useNavigate();
  const { state } = useLocation();
  const { enqueueSnackbar } = useSnackbar();
  const [open, setOpen] = React.useState(false);

  const [singleSiteEditorFormState, setSingleSiteEditorFormState] =
    useMergeState<SingleSiteEditorFormState>({
      isLoading: false,
      isSubmitting: false,
      errorMessage: null,
      attributeDefinitions: [],
      formInput: [],
      formId: generateUUIDV4(),
      hasCMDeployPermission: false,
      isSiteVisible: false,
    });

  const {
    isLoading,
    errorMessage,
    attributeDefinitions,
    isSubmitting,
    formInput,
    formId,
    hasCMDeployPermission,
    isSiteVisible,
  } = singleSiteEditorFormState;

  const numErrors = formInput.filter(i => !i.isValid).length;
  const numInputs = formInput.length;

  const canReset = useMemo(() => numInputs > 0, [numInputs]);
  const canSubmit = useMemo(
    () =>
      numInputs > 0 && numErrors <= 0 && hasCMDeployPermission && isSiteVisible,
    [formInput]
  );

  const handleBackClick = useCallback(() => {
    navigate(ASSET_MGMT_SITE_LIST);
  }, []);

  const handleResetClick = useCallback(() => {
    setSingleSiteEditorFormState({ formId: generateUUIDV4(), formInput: [] });
  }, []);

  const handleOnConfirmDialogSubmit = useCallback(async () => {
    refetchSiteAttributes(500);
    await sleep(500);
    handleResetClick();
  }, []);

  const { data: customAttrIds } = useCustomAttributesbySiteId({ siteId });

  const attributeIds: any[] = useMemo(() => {
    if (!customAttrIds?.results) return []; // Return an empty array if results are not available
    const ids = customAttrIds.results.flatMap(
      result => result?.variablesUsed?.attributeDefinitionIds ?? []
    );
    return Array.from(new Set(ids)); // Ensure unique values
  }, [customAttrIds]);

  const handleValueUpdate = useCallback(
    (
      definitionId: number,
      value: unknown,
      isValid: boolean,
      isUpdated: boolean
    ) => {
      const filteredInput = formInput.filter(
        i => i.attributeDefinitionId !== definitionId
      );
      if (!isUpdated) {
        setSingleSiteEditorFormState({ formInput: filteredInput });
        return null;
      }

      setSingleSiteEditorFormState({
        formInput: [
          ...filteredInput,
          {
            attributeDefinitionId: definitionId,
            value,
            isValid,
          } as CustomAttributeFormValue,
        ],
      });

      return null;
    },
    [singleSiteEditorFormState, setSingleSiteEditorFormState]
  );

  useEffect(() => {
    // re-render the form after staged attributes approval
    setTimeout(() => {
      setSingleSiteEditorFormState({ formId: generateUUIDV4() });
    }, 500);
  }, [renderKey]);

  useEffect(() => {
    setSingleSiteEditorFormState({
      hasCMDeployPermission: hasUserRole(UserRoles.CONFIG_MGMT_DEPLOY),
    });
    getSiteData(siteId).then(site => {
      setSingleSiteEditorFormState({ isSiteVisible: site.visible });
    });
  }, []);

  useEffect(() => {
    getAllCustomAttributeDefinition()
      .then(res => {
        setSingleSiteEditorFormState({ attributeDefinitions: res });
      })
      .catch(() => {
        enqueueSnackbar('Error fetching attributes definitions data', {
          variant: 'error',
        });
      })
      .finally(() => {
        setSingleSiteEditorFormState({ isLoading: false });
      });

    // Clear the location state
    // FIXME: uncommnet below line before production
    window.history.replaceState({}, document.title);
  }, [state]);

  const attributes = useMemo(
    () =>
      formInput.map(item => ({
        attributeDefinitionId: item.attributeDefinitionId,
        value: item.value,
      })),
    [formInput]
  );

  // transform 'proposed value' based on schema of the attribute
  const getTransFormedAttributeValue = useCallback(
    (schema: string, value: any) => {
      if (typeof schema === 'string') {
        try {
          const { type } = JSON.parse(schema);

          switch (type) {
            case 'number':
              return Number(value);
            case 'boolean':
              return value === 'true';
            case 'string':
              return String(value);
            default:
              return undefined;
          }
        } catch {
          return undefined;
        }
      }
      return undefined;
    },
    []
  );

  const formAttributes = useMemo(() => {
    const newAttributes = Object.keys(groupedSiteAttributes).map(
      (key: string | number) => {
        const attributeGroup = groupedSiteAttributes[key].filter(
          item => item.isStaged === false
        );
        const newAttributesGroupWithSchema: ICustomAttributeDefinitionWithStaged[] =
          [];
        if (attributeDefinitions.length > 0) {
          forEach(attributeGroup, attr => {
            const attributeDef = find(
              attributeDefinitions,
              item => item.id === attr.id
            );

            let transformedValue;
            if (attributeDef && attr.pendingApproval) {
              transformedValue = getTransFormedAttributeValue(
                attributeDef.schema,
                attr.value
              );
            }

            if (attributeDef) {
              const updatedAttr: ICustomAttributeDefinitionWithStaged = {
                ...attributeDef,
                defaultValue: attr.value,
                schemaDefaultValue: attributeDef?.defaultValue,
                doesStagedValueExist: attr.doesStagedValueExist,
                requiresApproval: attr.requiresApproval,
                pendingApproval: attr.pendingApproval,
                entityApprovalSettingId: attr.entityApprovalSettingId ?? null,
                // overwrite the default value with the transformed value if it exists
                ...(transformedValue !== undefined &&
                  transformedValue !== null && {
                    defaultValue: transformedValue,
                  }),
              };
              newAttributesGroupWithSchema.push(updatedAttr);
            }
          });
        }
        return newAttributesGroupWithSchema;
      }
    );
    return newAttributes;
  }, [groupedSiteAttributes, attributeDefinitions]);

  const hasCaApproverPermission = hasUserRole(UserRoles.SITE_CA_APPROVER);
  const isCompanyAdmin = hasUserRole(UserRoles.COMPANY_ADMIN);
  const isPowerUser = hasUserRole(UserRoles.POWER_USER);

  const dailogType = useMemo(() => {
    // flatten 'formAttributes'
    const ungroupedFormAttributes = formAttributes.flat();
    const ungroupedAttributesRequiringApproval = ungroupedFormAttributes.filter(
      attribute => attribute.requiresApproval
    );

    // if user is a attribute approver, do not show submit for approval button
    if (isCompanyAdmin || isPowerUser || hasCaApproverPermission) {
      return CustomAttributeConfirmDialogType.UPDATE;
    }
    // if user is not a attribute approver, check if any attribute requires approval
    const present = ungroupedAttributesRequiringApproval.some(attr =>
      formInput.some(input => input.attributeDefinitionId === attr.id)
    );
    return present
      ? CustomAttributeConfirmDialogType.SUBMIT_FOR_APPROVAL
      : CustomAttributeConfirmDialogType.UPDATE;
  }, [formAttributes, formInput]);

  const isArrayNameExists = arrayName =>
    Object.keys(groupedSiteAttributes).includes(arrayName);

  return (
    <Box
      display='flex'
      flex={1}
      flexGrow={1}
      flexShrink={0}
      justifyContent='stretch'
      sx={{ height: '100%' }}
    >
      {isLoading ? (
        <Box
          sx={{
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <CircularProgress />
        </Box>
      ) : (
        <Box
          sx={{
            position: 'relative',
            display: 'flex',
            height: '100%',
            flex: 1,
          }}
          key={formId}
        >
          <Box
            sx={{
              backgroundColor: 'common.backgroundLight',
              flex: 1,
              display: 'flex',
              flexDirection: 'column',
            }}
          >
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                height: '100%',
                width: '100%',
                flex: 1,
                p: 3,
                overflowY: 'auto',
                overflowX: 'hidden',
                gap: 2,
              }}
            >
              {children}
              <Paper
                variant='outlined'
                sx={{
                  position: 'relative',
                  flex: 1,
                  p: 3,
                  mb: 0,
                }}
              >
                <Box sx={{ flexDirection: 'row', display: 'flex' }}>
                  <Box sx={{ flex: 1 }}>
                    {!isLoading &&
                      !errorMessage &&
                      attributeDefinitions.length > 0 && (
                        <>
                          <Box sx={{ display: 'flex', width: '100%' }}>
                            <Box sx={{ flex: 1 }}>
                              <Typography
                                sx={{ fontSize: 'h2', fontWeight: 'bold' }}
                              >
                                Edit site attributes
                              </Typography>
                            </Box>
                            {updatedAt && (
                              <Box
                                sx={{
                                  display: 'flex',
                                  gap: 2,
                                  justifyContent: 'end',
                                }}
                              >
                                <Tooltip
                                  arrow
                                  placement='top'
                                  title={spacetime(new Date(updatedAt)).format(
                                    'dddd, MMM Do YYYY [at] h:mm a (UTCZ)'
                                  )}
                                >
                                  <Typography
                                    sx={{ fontSize: '11px', color: '#5D5D67' }}
                                  >
                                    {
                                      spacetime(new Date(updatedAt)).fromNow(
                                        new Date()
                                      ).precise
                                    }
                                  </Typography>
                                </Tooltip>
                              </Box>
                            )}
                          </Box>
                          <Box sx={{ pt: 1 }} key={1}>
                            {Boolean(formAttributes?.length) &&
                              formAttributes?.map((formAttribute, index) => {
                                const filteredFormAttribute =
                                  formAttribute.filter(
                                    item => item.groupName !== 'RDM Fields'
                                  );
                                return (
                                  <Fragment
                                    key={`form-${formAttribute[0].name}-${formAttribute[0].id}`}
                                  >
                                    <BulkEditorFormRenderer
                                      attributes={filteredFormAttribute}
                                      onValueUpdate={handleValueUpdate}
                                      disableForm={
                                        !hasCMDeployPermission || !isSiteVisible
                                      }
                                      customAttributeIds={attributeIds}
                                      isBulkUpdate={false}
                                    />
                                    {index === formAttributes.length - 1 &&
                                      formAttributes.length > 1 && (
                                        <Divider sx={{ my: 2 }} />
                                      )}
                                  </Fragment>
                                );
                              })}
                            <>
                              {Boolean(formAttributes?.length) &&
                                Boolean(isArrayNameExists('RDM Fields')) && (
                                  <Typography
                                    sx={{
                                      fontSize: 'h2',
                                      fontWeight: 'bold',
                                      display: 'flex',
                                      justifyContent: 'space-between',
                                      alignItems: 'center',
                                      padding: '2px 16px',
                                      width: 'calc(100% + 32px)',
                                      ml: '-16px',
                                      '&:hover': {
                                        backgroundColor:
                                          theme.palette.common.rowHover,
                                        cursor: 'pointer',
                                      },
                                    }}
                                    onClick={() => setOpen(!open)}
                                  >
                                    RDM Fields
                                    <IconButton
                                      sx={{ float: 'right' }}
                                      style={{
                                        outline: 'none',
                                        border: 'none',
                                      }}
                                      aria-label='expand row'
                                      onClick={() => setOpen(!open)}
                                    >
                                      {open ? (
                                        <KeyboardArrowUpIcon />
                                      ) : (
                                        <KeyboardArrowDownIcon />
                                      )}
                                    </IconButton>
                                  </Typography>
                                )}
                              <Collapse
                                in={open && Boolean(formAttributes?.length)}
                                timeout='auto'
                                unmountOnExit
                              >
                                {Boolean(formAttributes?.length) &&
                                  formAttributes?.map(formAttribute => {
                                    const filteredFormAttribute =
                                      formAttribute.filter(
                                        item => item.groupName === 'RDM Fields'
                                      );
                                    return (
                                      <BulkEditorFormRenderer
                                        attributes={filteredFormAttribute}
                                        onValueUpdate={handleValueUpdate}
                                        disableForm={
                                          !hasCMDeployPermission ||
                                          !isSiteVisible
                                        }
                                        customAttributeIds={attributeIds}
                                        isBulkUpdate={false}
                                      />
                                    );
                                  })}
                              </Collapse>
                            </>
                          </Box>
                        </>
                      )}

                    {errorMessage && (
                      <Box
                        sx={{
                          p: 3,
                          textAlign: 'center',
                          color: 'common.errorRed',
                        }}
                      >
                        Error fetching attributes definitions data
                      </Box>
                    )}
                  </Box>
                </Box>
              </Paper>
            </Box>
            <Paper
              sx={{
                px: 6,
                display: 'flex',
                flexDirection: 'row',
                flexGrow: 0,
                flexShrink: 0,
                gap: 1,
                width: '100%',
                justifyContent: 'flex-end',
                borderRadius: 0,
                borderLeft: 0,
                borderRight: 0,
              }}
              variant='outlined'
            >
              <Box sx={{ height: '100%', flexGrow: 1, display: 'flex' }}>
                <Typography sx={{ alignSelf: 'center' }}>
                  {numInputs > 0 && (
                    <Typography component='span' variant='subtitle2'>
                      {numInputs} attributes edited
                    </Typography>
                  )}
                  {numErrors > 0 && <span>, </span>}
                  {numErrors > 0 && (
                    <Typography
                      component='span'
                      variant='subtitle2'
                      color='common.errorRed'
                    >
                      {numErrors} invalid entry
                    </Typography>
                  )}
                </Typography>
              </Box>
              <Button
                variant='outlined'
                sx={{ minWwidth: '100px' }}
                onClick={handleBackClick}
              >
                Back
              </Button>
              <Button
                disabled={!canReset}
                variant='outlined'
                sx={{ minWwidth: '100px' }}
                onClick={handleResetClick}
              >
                Reset
              </Button>
              <Suspense fallback={<CircularProgress />}>
                <CustomAttributeConfirmDialog
                  attributes={attributes}
                  canSubmit={canSubmit}
                  defaultDeploymentType={
                    IUpdateCustomAttributeDeploymentType.maintenanceWindow
                  }
                  entityType={Entities.Sites}
                  isLoading={isLoading || isAttributesLoading}
                  onSubmit={handleOnConfirmDialogSubmit}
                  siteIds={[siteId]}
                  isBulkUpdate={false}
                  dailogType={dailogType}
                />
              </Suspense>
            </Paper>
            <Backdrop
              open={
                isSubmitting || isLoading || attributeDefinitions.length === 0
              }
              sx={{
                backgroundColor: 'rgba(255,255,255,.7)',
                zIndex: 10,
                position: 'absolute',
              }}
            >
              <CircularProgress size={36} />
            </Backdrop>
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default SingleSiteEditorForm;
